<?php
include_once '../lib/config.php';
user();
$uid = $_SESSION['userid'];
$user = get_user_details($uid);
$_address = strtolower(SITE_CURRENCY_) . '_address';
$typearr = array(6 => 'DOT', 7 => 'TRX', 8 => 'LINK', 9 => 'BNB', 10 => 'BTC', 11 => 'ETC');
?>
<!DOCTYPE html>
<html lang="en" data-theme="light">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="author" content="">
    <meta name="description" content="<?php echo isset($description) ? $description : ''; ?>">
    <meta name="keywords" content="<?php echo isset($keywords) ? $keywords : ''; ?>" />
    <title>
        <?php echo $title_name = isset($title) ? SITE_NAME . ' | ' . str_replace('COIN_NAME', SITE_CURRENCY, $title) : SITE_NAME . ' | Member Panel'; ?>
    </title>
    <link rel="shortcut icon" href="images/nexabot-logo.png" type="image/x-icon">
    <script src="https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js"></script>
    <script>
        WebFont.load({
            google: {
                families: ['Alegreya+Sans:100,100i,300,300i,400,400i,500,500i,700,700i,800,800i,900,900i', 'Raleway:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i', 'Open Sans']
            }
        });
    </script>
    <!-- START GLOBAL MANDATORY STYLE -->
    <link href="../assets/dist/css/base.css" rel="stylesheet" type="text/css" />
    <!-- START PAGE LABEL PLUGINS -->
    <link href="../assets/plugins/datatables/dataTables.min.css" rel="stylesheet" type="text/css" />
    <?php if ($_SERVER["PHP_SELF"] == '/soft/admin/dashboard.php' || (isset($_is_dashboard) && $_is_dashboard)) { ?>
        <link href="../assets/plugins/toastr/toastr.min.css" rel=stylesheet type="text/css" />
        <link href="../assets/plugins/emojionearea/emojionearea.min.css" rel=stylesheet type="text/css" />
        <link href="../assets/plugins/monthly/monthly.min.css" rel=stylesheet type="text/css" />
        <link href="../assets/plugins/amcharts/export.css" rel=stylesheet type="text/css" />
    <?php } ?>
    <!-- START THEME LAYOUT STYLE -->
    <link href="../assets/dist/css/component_ui.min.css" rel="stylesheet" type="text/css" />
    <?php /*<link id="defaultTheme" href="../assets/dist/css/skins/component_ui_black.css" rel="stylesheet" type="text/css"/>
   <link href="../assets/dist/css/component_ui_black.css" rel="stylesheet" type="text/css"/>*/ ?>
    <link id="defaultTheme" href="../assets/dist/css/skins/skin-blue.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css"
        integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous">

    <link href="../assets/dist/css/custom.css" rel="stylesheet" type="text/css" />
    <?php /*<link id="defaultTheme" href="../assets/dist/css/skins/skin-red-dark.css" rel="stylesheet" type="text/css"/>*/ ?>
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
            <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
            <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
        <![endif]-->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            /* Light theme variables as default */
            --primary-bg: #ffffff;
            --secondary-bg: #f8f9fa;
            --hover-bg: #e9ecef;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --accent-color: #007bff;
            --border-color: rgba(0, 0, 0, 0.125);
            --success-color: #28a745;
            --danger-color: #dc3545;
            --card-bg: #ffffff;
        }

        /* Dark theme variables */
        [data-theme="dark"] {
            --primary-bg: #0b0e11;
            --secondary-bg: #1e2329;
            --hover-bg: #2b3139;
            --text-primary: #eaecef;
            --text-secondary: #848e9c;
            --accent-color: #f0b90b;
            --border-color: #2c3137;
            --success-color: #02c076;
            --danger-color: #f6465d;
            --card-bg: #2b3139;
        }

        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        .slide-in {
            animation: slideIn 0.3s ease-in-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(-20px);
                opacity: 0;
            }

            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Modern Navbar */
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            height: 65px;
            padding: 0 25px;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }
@media (min-width: 768px) {
    #page-wrapper {
            position: inherit;
            margin-left: 70px;
            padding: 0 30px 30px;
        }
        .sidebar{
            width:80px;
        }
}
@media (max-width: 768px) {
        .menu-text {
    display: block;
    opacity: 1;
    transition: opacity 0.3s ease;
}
        .sidebar{
            width:auto !important;
        }
}

        .navbar-brand {
            padding: 8px 15px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            backdrop-filter: blur(10px);
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            background: rgba(255, 255, 255, 0.2);
        }

        .navbar-brand img {
            height: 38px;
            transition: transform 0.3s ease;
        }

        .navbar-brand span {
            color: white;
            font-weight: 700;
            font-size: 18px;
            margin-left: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* Fresh Modern Responsive Sidebar */
        .sidebar {
            width: 280px;
            position: fixed;
            height: 100vh;
            left: -280px;
            top: 65px;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            z-index: 999;
            box-shadow: 4px 0 20px rgba(79, 70, 229, 0.3);
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
            pointer-events: none;
        }

        .sidebar.open {
            left: 0;
        }

        /* Large screen - sidebar on left */
        @media (min-width: 1200px) {
            .sidebar {
                width: 100%;
                max-width: 400px;
                height: 80vh;
                left: 50%;
                top: auto;
                bottom: -80vh;
                transform: translateX(-50%);
                border-radius: 25px 25px 0 0;
                border-right: none;
                border: 1px solid rgba(0, 0, 0, 0.1);
                box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.15);
            }

            .sidebar.open {
                bottom: 0;
                left: 50%;
            }
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Fresh Rotate Animation */
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Sidebar Handle */
        .sidebar-handle {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 4px;
            background: #dee2e6;
            border-radius: 2px;
            cursor: pointer;
        }

        /* Modern Bottom Sidebar Menu Items */
        #side-menu {
            padding: 30px 20px 20px;
            list-style: none;
            margin: 0;
        }

        #side-menu li {
            position: relative;
            margin: 8px 0;
            border-radius: 12px;
            overflow: hidden;
        }

        #side-menu li a {
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: white;
            text-decoration: none;
            white-space: nowrap;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 15px;
            position: relative;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            z-index: 2;
        }

        #side-menu li a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
            border-radius: 12px;
        }

        #side-menu li a:hover::before {
            left: 100%;
        }

        .menu-item-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* Fresh Sidebar Header */
        .sidebar-header {
            padding: 24px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            color: white;
            margin-bottom: 16px;
            position: relative;
            z-index: 2;
            backdrop-filter: blur(10px);
        }

        .sidebar-title {
            font-size: 1.2rem;
            font-weight: 800;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* Modern Responsive Submenu styling */
        .nav-second-level {
            background: rgba(102, 126, 234, 0.05);
            margin: 8px 0 0 0;
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            border-radius: 8px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            display: block !important;
        }

        .nav-second-level.show {
            max-height: 500px;
            padding: 8px;
        }

        .nav-second-level li {
            margin: 4px 0;
            border-radius: 8px;
        }

        .nav-second-level li a {
            padding: 12px 16px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            display: block;
            text-decoration: none;
            backdrop-filter: blur(10px);
        }

        .nav-second-level li a::before {
            content: '';
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 4px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .nav-second-level li a:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .nav-second-level li a:hover::before {
            background: white;
            transform: translateY(-50%) scale(1.5);
            box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
        }

        /* Large screen specific submenu */
        @media (min-width: 1200px) {
            .nav-second-level {
                border-radius: 12px;
                background: rgba(102, 126, 234, 0.08);
            }
        }

        /* Show submenu on hover */
        .sidebar:not(.expanded) #side-menu li:hover > .nav-second-level {
            opacity: 1;
            visibility: visible;
            transform: translateX(0);
        }

        /* Submenu items in collapsed state */
        .sidebar:not(.expanded) .nav-second-level li a {
            padding: 10px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .sidebar:not(.expanded) .nav-second-level li a:hover {
            background: var(--hover-bg);
        }

        /* Submenu styling for expanded state */
        .sidebar.expanded .nav-second-level {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.1);
        }

        .sidebar.expanded .nav-second-level.show {
            max-height: 500px;
        }

        /* Arrow indicator */
        .fa.arrow {
            margin-left: auto;
            transition: transform 0.3s ease;
        }

        .sidebar.expanded li a[aria-expanded="true"] .fa.arrow {
            transform: rotate(180deg);
        }

        /* Fresh hover effect for sidebar menu items */
        #side-menu > li > a:hover {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        #side-menu > li > a.active {
            background: rgba(255, 255, 255, 0.25);
            color: white;
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(10px);
        }

        #side-menu > li > a.active i {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }

        /* Fresh Accordion Arrow */
        .accordion-arrow {
            font-size: 12px;
            transition: transform 0.3s ease;
            color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.15);
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .accordion-arrow.open {
            transform: rotate(180deg);
            background: rgba(255, 255, 255, 0.25);
            color: white;
            box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
        }

        /* Has dropdown indicator */
        .has-dropdown > a .accordion-arrow {
            display: flex;
        }

        #side-menu li:not(.has-dropdown) .accordion-arrow {
            display: none;
        }

        /* Animation for submenu items */
        .nav-second-level li {
            opacity: 0;
            transform: translateX(-10px);
            transition: all 0.3s ease;
        }

        .nav-second-level.show li,
        .sidebar:not(.expanded) #side-menu li:hover > .nav-second-level li {
            opacity: 1;
            transform: translateX(0);
        }

        /* Delayed animation for submenu items */
        .nav-second-level li:nth-child(1) { transition-delay: 0.1s; }
        .nav-second-level li:nth-child(2) { transition-delay: 0.2s; }
        .nav-second-level li:nth-child(3) { transition-delay: 0.3s; }
        .nav-second-level li:nth-child(4) { transition-delay: 0.4s; }
        .nav-second-level li:nth-child(5) { transition-delay: 0.5s; }

        /* Tooltip for collapsed state */
        .sidebar:not(.expanded) #side-menu > li > a:hover::after {
            content: attr(data-title);
            position: absolute;
            left: 70px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--card-bg);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1000;
            border: 1px solid var(--border-color);
        }

        /* Hide tooltip if item has submenu */
        .sidebar:not(.expanded) #side-menu > li:hover > a[data-has-submenu]::after {
            display: none;
        }

        /* Fresh Sidebar Icon styling */
        #side-menu li a i {
            min-width: 20px;
            text-align: center;
            font-size: 16px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        #side-menu li a:hover i {
            background: rgba(255, 255, 255, 0.25);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
        }

        /* Fresh Menu text always visible */
        .menu-text {
            display: block;
            opacity: 1;
            font-weight: 600;
            flex: 1;
            color: white;
        }

        /* Modern Toggle button for bottom sidebar */
        .menu-toggle {
            background: rgba(255, 255, 255, 0.15);
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .menu-toggle:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }

        /* Bottom sidebar specific styles */
        @media (max-width: 768px) {
            .sidebar {
                max-width: 100%;
                border-radius: 20px 20px 0 0;
            }
        }

        /* Active state */
        #side-menu li a.active,
        #side-menu li a:hover {
            background: var(--hover-bg);
        }

        /* Arrow for submenu */
        .fa.arrow {
            margin-left: auto;
            font-size: 12px;
            transition: transform 0.3s ease;
            opacity: 0;
        }

        .sidebar.expanded .fa.arrow {
            opacity: 1;
        }

        /* Submenu */
        .nav-second-level {
            display: none;
            padding-left: 15px;
            background: rgba(0, 0, 0, 0.1);
        }

        .sidebar.expanded .nav-second-level {
            display: block;
        }

        .nav-second-level li a {
            padding: 10px 15px 10px 35px;
            font-size: 13px;
            color: #6c757d;
            border-radius: 4px;
            margin: 2px 10px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-second-level li a::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 4px;
            background: #007bff;
            border-radius: 50%;
        }

        .nav-second-level li a:hover {
            background: #e9ecef;
            color: #007bff;
        }

        /* Arrow icon adjustments */
        .fa.arrow {
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar.expanded .fa.arrow {
            opacity: 1;
        }

        /* Submenu adjustments */
        .nav-second-level {
            padding-left: 0;
        }

        .sidebar.expanded .nav-second-level {
            padding-left: 15px;
        }

        /* Fix for arrow alignment */
        .fa.arrow {
            margin-left: auto;
            font-size: 12px;
            transition: transform 0.2s ease;
        }

        #side-menu li a[aria-expanded="true"] .fa.arrow {
            transform: rotate(180deg);
        }

        /* Remove any additional borders or lines */
        #side-menu li {
            border: none;
        }

        #side-menu li a:hover {
            background-color: var(--hover-bg);
        }

        /* Fix for nested menu items */
        .nav-second-level {
            padding-left: 15px;
        }

        .nav-second-level li a {
            padding-left: 30px !important;
        }

        /* Remove any unwanted borders */
        .sidebar-nav {
            border: none;
        }

        .nav.nav-second-level {
            background: transparent;
        }

        /* Theme Switcher */
        .theme-switcher {
            position: fixed;
            bottom: 24px;
            right: 24px;
            background-color: var(--card-bg);
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .theme-switcher:hover {
            transform: scale(1.1);
        }

        /* Settings Panel */
        .settings-panel {
            position: fixed;
            right: -300px;
            top: 64px;
            width: 300px;
            height: calc(100vh - 64px);
            background-color: var(--card-bg);
            border-left: 1px solid var(--border-color);
            transition: right 0.3s ease;
            z-index: 998;
            padding: 24px;
        }

        .settings-panel.active {
            right: 0;
        }

        /* Modern Colorful Top Navigation Icons */
        .navbar-top-links {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .navbar-top-links li a {
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
        }

        .navbar-top-links li a:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .navbar-top-links li a i {
            font-size: 18px;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }

            100% {
                transform: scale(1);
            }
        }

        .mobile-logout {
            display: none;
        }

        .custom-logout-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #dc3545;
            border-radius: 6px;
            color: white;
            transition: all 0.3s ease;
            border: 1px solid #dc3545;
            font-weight: 500;
            font-size: 14px;
        }

        .custom-logout-btn:hover {
            background: #c82333;
            border-color: #c82333;
            color: white;
        }

        .logout-icon-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logout-text {
            font-weight: 500;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        @media screen and (max-width: 768px) {
            .logout-text {
                display: none;
            }

            .custom-logout-btn {
                padding: 8px;
            }
        }

        .logout-btn i {
            font-size: 16px;
        }

        @media screen and (max-width: 768px) {
            .mobile-logout {
                display: block;
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 999;
            }

            .logout-btn {
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            }

            /* Hide desktop logout if it exists */
            .desktop-logout {
                display: none;
            }
        }
        #page-wrapper {
            padding: 20px !important;
            min-height: calc(100vh - 65px);
            margin-left: 0;
            transition: all 0.4s ease;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        /* Left sidebar for mobile/tablet */
        .sidebar.open ~ #page-wrapper {
            margin-left: 280px;
        }

        /* Large screen - no margin change for bottom sidebar */
        @media (min-width: 1200px) {
            .sidebar.open ~ #page-wrapper {
                margin-left: 0;
            }
        }

        #wrapper {
            position: relative;
            margin-top: 65px;
        }

        /* Overlay for bottom sidebar */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4);
            z-index: 998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            backdrop-filter: blur(2px);
        }

        .sidebar-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* Floating Action Button for Menu */
        .floating-menu-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 24px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            display: block;
        }

        .floating-menu-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .floating-menu-btn.active {
            transform: rotate(45deg);
        }

        /* Hide floating button on mobile/tablet, show hamburger in navbar */
        @media (max-width: 1199px) {
            .floating-menu-btn {
                display: none;
            }
        }

        /* Hamburger menu for mobile/tablet */
        .mobile-menu-toggle {
            background: rgba(255, 255, 255, 0.15);
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: none;
        }

        @media (max-width: 1199px) {
            .mobile-menu-toggle {
                display: block;
            }
        }

        .mobile-menu-toggle:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }
        @media screen and (max-width: 480px) {
            .mobile-logout {
                bottom: 15px;
                right: 15px;
            }

            .logout-btn {
                padding: 8px 12px;
            }

            .logout-btn span {
                display: none;
            }

            .logout-btn i {
                font-size: 20px;
            }
        }

        .navbar-right {
            position: absolute;
            right: 0;
        }

        .navbar-top-links>li.log_out a {
            padding: 15px 15px !important;
        }
    </style>
</head>

<body>
    <div id="wrapper" class="wrapper animsition">
        <!-- Modern Navigation -->
        <nav class="navbar navbar-fixed-top">
            <button type="button" class="mobile-menu-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
             <a class="navbar-brand" href="dashboard.php">
                        <?php if(file_exists('images/nexabot-logo.png')){?>
                        <img class="main-logo" src="images/nexabot-logo.png" id="bg" alt="<?php echo SITE_NAME; ?>">
                        <?php }else{?>
                        <span><?php echo SITE_NAME; ?></span>
                        <?php }?>
                    </a>
            <!--<ul class="nav navbar-nav hidden-xs">-->
            <!--    <li><a id="fullscreen" href="#"><i class="material-icons">fullscreen</i> </a></li>-->
            <!--</ul>-->
            <ul class="nav navbar-top-links navbar-right">
                <li class="dropdown">
                    <a class="dropdown-toggle" href="email_inbox.php">
                        <i class="material-icons">chat</i>
                        <span class="label label-danger"><?php echo get_unread_message_count($uid); ?></span>
                    </a>
                </li><!-- /.Dropdown -->
                <?php /*<li class="dropdown">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</li>
                   <li class="dropdown">
                       <a class="dropdown-toggle" data-toggle="dropdown" href="javascript:void(0);">
                           <i class="material-icons">person_add</i>
                       </a>
                       <ul class="dropdown-menu dropdown-user">
                           <?php /*<li><a href="profile.php"><i class="ti-user"></i>&nbsp; Profile</a></li>*/ ?>
                <?php /*<li><a href="report_login.php"><i class="ti-lock"></i>&nbsp; Login Details</a></li>*?>
                           <li><a href="logout.php"><i class="ti-layout-sidebar-left"></i>&nbsp; Logout</a></li>
                       </ul><!-- /.dropdown-user -->
                   </li><!-- /.Dropdown -->*/ ?>
                <li class="log_out">
                    <a href="logout.php" class="custom-logout-btn">
                        <span class="logout-icon-wrapper">
                            <i class="fas fa-sign-out-alt"></i>
                        </span>
                    </a>
                </li><!-- /.Log out -->
            </ul> <!-- /.navbar-top-links -->
            <!-- </div> -->

        </nav>
        <!-- /.Navigation -->
        <div class=" sidebar sidebar-nav navbar-collapse">
            <div class="sidebar-nav navbar-collapse">
                <ul class="nav" id="side-menu">

                    <li>
                        <a href="dashboard.php" data-title="Dashboard">
                            <div class="menu-item-content">
                                <i class="fas fa-home"></i>
                                <span class="menu-text">Dashboard</span>
                            </div>
                        </a>
                    </li>
                    <!--<li><a href="dashboard2.php" class="material-ripple"><i class="fas fa-chart-line"></i> Validator Dashboard</a></li>-->
                    
                    <!-- Profile Section -->
                    <li>
                        <a href="#" class="material-ripple"><i class="fas fa-user-circle"></i> Profile<span
                                class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                            <li><a href="profile.php"><i class="fas fa-user"></i> My Profile</a></li>
                            <li><a href="change_password.php"><i class="fas fa-lock"></i> Change Password</a></li>
                        </ul>
                    </li>

                    <li>
                        <a href="#" class="material-ripple"><i class="fas fa-users"></i> Team<span
                                class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                            <li><a href="direct_referral.php" class="material-ripple"><i class="fas fa-directions"></i>
                                    My Direct</a></li>
                            <li><a href="downline.php" class="material-ripple"><i class="fas fa-users"></i> My Team</a>
                            </li>
                            <?php /*<li><a href="tree_view.php" class="material-ripple"><i class="material-icons">bubble_chart</i> My Tree</a></li>*/ ?>
                        </ul>
                    </li>

                     <li>
                        <a href="invest.php" data-title="Trade">
                            <i class="fas fa-chart-bar icon"></i>
                            <span class="menu-text">Trade</span>
                        </a>
                    </li>

                    <!--<li><a href="invest_new.php?type=3" class="material-ripple"><i class="fas fa-money-bill"></i> Stacking Coins</a></li>-->

                    <li>
                        <a href="#" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i> Trading
                            Reports<span class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                                <li><a href="report_invest.php" class="material-ripple"><i class="fas fa-money-check-alt"></i> Investments History</a></li>
                                <li><a href="report_growth.php" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i> ROI Income</a></li>
                                <li><a href="report_direct.php" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i> Referral Income</a></li>
                                <li><a href="report_level.php" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i>Level Income</a></li>
                                <li><a href="report_royalty.php" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i>Reward Income</a></li>
                                <li><a href="report_royalty.php?type=1" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i>Royalty Income</a></li>

                            <?php /*
                                <li><a href="report_royalty.php?type=2" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i>Special Reward Income</a></li>
                                <li><a href="report_level.php?type=1" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i>Upline Income</a></li>
                                <li><a href="report_level.php?type=2" class="material-ripple"><i class="fa fa-usd" aria-hidden="true"></i>Level ROI Income</a></li>
                                <li><a href="report_royalty.php?type=2" class="material-ripple"><i class="material-icons">business</i> Airdrop Income</a></li>
                           <li><a href="report_direct.php?type=2" class="material-ripple"><i class="material-icons">business</i> Referral Airdrop Rewad Income</a></li>
                           <li><a href="report_level.php?type=1" class="material-ripple"><i class="material-icons">business</i> Level Airdrop Income</a></li>*/ ?>

                            
                        </ul>
                    </li>

                    <!--<li>-->
                    <!--    <a href="#" class="material-ripple"><i class="fa fa-video-camera" aria-hidden="true"></i> Adding-->
                    <!--        Videos<span class="fa arrow"></span></a>-->
                    <!--    <ul class="nav nav-second-level">-->
                    <!--        <li><a href="video.php" class="material-ripple"><i class="fa fa-plus-circle"-->
                    <!--                    aria-hidden="true"></i> Submit Video</a></li>-->
                    <!--        <li><a href="report_videos.php" class="material-ripple"><i class="fa fa-video-camera"-->
                    <!--                    aria-hidden="true"></i> Videos</a></li>-->
                    <!--    </ul>-->
                    <!--</li>-->

                  <li>
                            <a href="#" class="material-ripple"><i class="fas fa-exchange-alt"></i> Fund Management<span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <?php /*<li><a href="deposit_block.php" class="material-ripple"><i class="fas fa-cart-plus"></i> Add Fund by <?php echo SITE_CURRENCY_TKN; ?></a></li>*/?>
                                <li><a href="deposit_block.php" class="material-ripple"><i class="fas fa-cart-plus"></i> Add Fund by <?php echo SITE_CURRENCY_TKN; ?></a></li>
                                <li><a href="report_deposit_block.php" class="material-ripple"><i class="fas fa-history"></i> Deposit History</a></li>
                                <li><a href="withdrawal_block.php?type=10" class="material-ripple"><i class="material-icons">insert_emoticon</i> Withdrawal</a></li>
                                <li><a href="report_withdrawal_block.php" class="material-ripple"><i class="fas fa-history"></i> Withdrawal History</a></li>
                            </ul>
                        </li>

                 <li>
                            <a href="#" class="material-ripple"><i class="fas fa-money-bill-alt"></i> Fund Transfer<span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <!--<li><a href="fund_transfer.php?type=1" class="material-ripple"><i class="fas fa-exchange-alt"></i> Topup Fund Transfer</a></li>-->
                                <!--<li><a href="fund_transfer3.php?type=1" class="material-ripple"><i class="fas fa-exchange-alt"></i> Self Fund Transfer</a></li>-->
                                <?php /*<li><a href="fund_transfer2.php" class="material-ripple"><i class="fas fa-exchange-alt"></i> Fund Transfer to Game</a></li>*/?>
                                <li><a href="report_fund_transfer.php" class="material-ripple"><i class="fas fa-history"></i> Fund Transfer History</a></li>
                            </ul>
                        </li>
                    <!--<li>-->
                    <!--    <a href="report_deposit_block.php" data-title="Deposit History">-->
                    <!--        <i class="fas fa-history">-->
                    <!--        <span class="menu-text">Dashboard</span>-->
                    <!--    </a>-->
                    <!--</li>-->
                   
                    <li>
                        <a href="#" class="material-ripple"><i class="fas fa-mail-bulk"></i> Support<span
                                class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                                <li><a href="email_compose_mail.php">Compose</a></li>
                                <li><a href="email_inbox.php">Inbox</a></li>
                                <li><a href="email_sent_mail.php">Sent</a></li>
                            </ul>
                    </li>
                </ul>
            </div>
            <!-- /.sidebar-collapse -->
        </div>
        <!-- /.Left Sidebar-->
        <!-- /.Navbar  Static Side -->
        <div class="control-sidebar-bg"></div>
        <!-- Page Content -->
        <div id="page-wrapper">
            <!-- main content -->
            <div class="content">
                <?php /*<div class="row">
                   <div class="col-sm-12 col-md-6">
                       <a style="cursor:pointer;color:#fff;border-radius:10px;padding: 5px 10px;align-items: center;display:inline-flex;background: #5b69bc;margin-top: 10px;margin-bottom: 10px;" href="<?php echo (SITE_CURRENCY_ == 'TRX') ? 'https://tronscan.org/#/contract' : ((SITE_CURRENCY_ == 'BNB') ? 'https://bscscan.com/address' : 'https://etherscan.io/address'); ?>/<?php echo CONTRACT_ADDRESS;?>" target="_blank">Contract Address: <?php echo CONTRACT_ADDRESS;?> <i class="fa fa-external-link"></i></a>
                   </div>
                   <div class="col-sm-12 col-md-6">
                       <a style="cursor:pointer;color:#fff;border-radius:10px;padding: 5px 10px;align-items: center;display:inline-flex;background: #5b69bc;margin-top: 10px;margin-bottom: 10px;" href="<?php echo (SITE_CURRENCY_ == 'TRX') ? 'https://tronscan.org/#/address' : ((SITE_CURRENCY_ == 'BNB') ? 'https://bscscan.com/address' : 'https://etherscan.io/address'); ?>/<?php echo $user->bnb_address;?>" target="_blank">Your Address: <?php echo $user->bnb_address;?> <i class="fa fa-external-link"></i></a>
                   </div>
               </div>*/ ?>
                <!-- Content Header (Page header) -->
                <div class="content-header">
                    <div class="header-icon"><i
                            class="pe-7s-<?php echo isset($titleicon) ? $titleicon : 'graph1'; ?>"></i></div>
                    <div class="header-title">
                        <h1><?php echo isset($title) ? $title : ''; ?></h1>
                    </div>
                </div> <!-- /. Content Header (Page header) -->
                <div class="row">
                    <div class="col-sm-12 col-md-12">
                        <?php echo getMessage(); ?>
                    </div>
                </div>
                <script>
                    // Modern Bottom Slide-up Sidebar Functions
                    function toggleSidebar() {
                        const sidebar = document.querySelector('.sidebar');
                        const overlay = document.querySelector('.sidebar-overlay');
                        const floatingBtn = document.querySelector('.floating-menu-btn');

                        sidebar.classList.toggle('open');
                        if (overlay) {
                            overlay.classList.toggle('active');
                        }
                        if (floatingBtn) {
                            floatingBtn.classList.toggle('active');
                        }
                    }

                    function closeSidebar() {
                        const sidebar = document.querySelector('.sidebar');
                        const overlay = document.querySelector('.sidebar-overlay');
                        const floatingBtn = document.querySelector('.floating-menu-btn');

                        sidebar.classList.remove('open');
                        if (overlay) {
                            overlay.classList.remove('active');
                        }
                        if (floatingBtn) {
                            floatingBtn.classList.remove('active');
                        }
                    }

                    document.addEventListener('DOMContentLoaded', function() {
                        // Create overlay for bottom sidebar
                        const overlay = document.createElement('div');
                        overlay.className = 'sidebar-overlay';
                        overlay.addEventListener('click', closeSidebar);
                        document.body.appendChild(overlay);

                        // Create floating menu button for large screens
                        const floatingBtn = document.createElement('button');
                        floatingBtn.className = 'floating-menu-btn';
                        floatingBtn.innerHTML = '<i class="fas fa-bars"></i>';
                        floatingBtn.addEventListener('click', toggleSidebar);
                        document.body.appendChild(floatingBtn);

                        // Check screen size and show appropriate button
                        function updateMenuButton() {
                            const isLargeScreen = window.innerWidth >= 1200;
                            const mobileToggle = document.querySelector('.mobile-menu-toggle');

                            if (isLargeScreen) {
                                floatingBtn.style.display = 'block';
                                if (mobileToggle) mobileToggle.style.display = 'none';
                            } else {
                                floatingBtn.style.display = 'none';
                                if (mobileToggle) mobileToggle.style.display = 'block';
                            }
                        }

                        window.addEventListener('resize', updateMenuButton);
                        updateMenuButton();

                        // Add sidebar header and handle for large screens
                        const sidebar = document.querySelector('.sidebar');
                        if (sidebar) {
                            function updateSidebarHeader() {
                                const isLargeScreen = window.innerWidth >= 1200;
                                let sidebarHeader = sidebar.querySelector('.sidebar-header');
                                let sidebarHandle = sidebar.querySelector('.sidebar-handle');

                                if (isLargeScreen) {
                                    // Add header and handle for bottom sidebar
                                    if (!sidebarHeader) {
                                        sidebarHeader = document.createElement('div');
                                        sidebarHeader.className = 'sidebar-header';
                                        sidebarHeader.innerHTML = '<h3 class="sidebar-title">Navigation Menu</h3>';
                                        sidebar.insertBefore(sidebarHeader, sidebar.firstChild);
                                    }

                                    if (!sidebarHandle) {
                                        sidebarHandle = document.createElement('div');
                                        sidebarHandle.className = 'sidebar-handle';
                                        sidebarHandle.addEventListener('click', closeSidebar);
                                        sidebar.insertBefore(sidebarHandle, sidebar.firstChild);
                                    }
                                } else {
                                    // Remove header and handle for left sidebar
                                    if (sidebarHeader) sidebarHeader.remove();
                                    if (sidebarHandle) sidebarHandle.remove();
                                }
                            }

                            window.addEventListener('resize', updateSidebarHeader);
                            updateSidebarHeader();
                        }

                        // Add has-dropdown class and accordion arrows
                        const menuItems = document.querySelectorAll('#side-menu li');
                        menuItems.forEach(item => {
                            const mainLink = item.querySelector('a');
                            const submenu = item.querySelector('.nav-second-level');

                            if (submenu) {
                                item.classList.add('has-dropdown');

                                // Add accordion arrow
                                const arrow = document.createElement('i');
                                arrow.className = 'fas fa-chevron-down accordion-arrow';
                                mainLink.appendChild(arrow);

                                // Add click handler for accordion toggle
                                mainLink.addEventListener('click', function(e) {
                                    e.preventDefault();

                                    // Close other open accordions
                                    menuItems.forEach(otherItem => {
                                        if (otherItem !== item) {
                                            const otherSubmenu = otherItem.querySelector('.nav-second-level');
                                            const otherArrow = otherItem.querySelector('.accordion-arrow');
                                            if (otherSubmenu && otherArrow) {
                                                otherSubmenu.classList.remove('show');
                                                otherArrow.classList.remove('open');
                                            }
                                        }
                                    });

                                    // Toggle current accordion
                                    submenu.classList.toggle('show');
                                    arrow.classList.toggle('open');
                                });
                            }
                        });

                        // Handle mobile responsiveness for bottom sidebar
                        function adjustForMobile() {
                            const isMobile = window.innerWidth < 768;
                            const sidebar = document.querySelector('.sidebar');
                            const floatingBtn = document.querySelector('.floating-menu-btn');

                            if (isMobile) {
                                sidebar.classList.remove('open');
                                if (floatingBtn) {
                                    floatingBtn.style.bottom = '20px';
                                    floatingBtn.style.right = '20px';
                                }
                            }
                        }

                        window.addEventListener('resize', adjustForMobile);
                        adjustForMobile();

                        // Close sidebar when clicking outside
                        document.addEventListener('click', function(e) {
                            const sidebar = document.querySelector('.sidebar');
                            const floatingBtn = document.querySelector('.floating-menu-btn');

                            if (!sidebar.contains(e.target) && !floatingBtn.contains(e.target)) {
                                closeSidebar();
                            }
                        });

                        // Swipe down to close sidebar
                        let startY = 0;
                        const sidebarElement = document.querySelector('.sidebar');

                        if (sidebarElement) {
                            sidebarElement.addEventListener('touchstart', function(e) {
                                startY = e.touches[0].clientY;
                            });

                            sidebarElement.addEventListener('touchend', function(e) {
                                const endY = e.changedTouches[0].clientY;
                                const diff = startY - endY;

                                // If swipe down more than 50px, close sidebar
                                if (diff < -50) {
                                    closeSidebar();
                                }
                            });
                        }
                    });
                </script>
                <script>
                    document.addEventListener('DOMContentLoaded', function () {
                        // Theme Switcher
                        const themeSwitcher = document.getElementById('themeSwitcher');
                        const html = document.documentElement;

                        themeSwitcher.addEventListener('click', function () {
                            const currentTheme = html.getAttribute('data-theme');
                            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                            html.setAttribute('data-theme', newTheme);

                            // Update icon
                            const icon = this.querySelector('i');
                            icon.textContent = newTheme === 'dark' ? 'dark_mode' : 'light_mode';

                            // Save preference
                            localStorage.setItem('theme', newTheme);
                        });

                        // Settings Panel Toggle
                        const settingsToggle = document.getElementById('settingsToggle');
                        const settingsPanel = document.getElementById('settingsPanel');

                        settingsToggle.addEventListener('click', function (e) {
                            e.preventDefault();
                            settingsPanel.classList.toggle('active');
                        });

                        // Sidebar Toggle
                        const sidebarToggle = document.getElementById('sidebarToggle');
                        const sidebar = document.querySelector('.sidebar');

                        sidebarToggle.addEventListener('click', function () {
                            sidebar.classList.toggle('active');
                        });

                        // Add slide-in animation to menu items
                        const menuItems = document.querySelectorAll('#side-menu li a');
                        menuItems.forEach((item, index) => {
                            item.style.animationDelay = `${index * 0.1}s`;
                            item.classList.add('slide-in');
                        });

                        // Load saved theme preference
                        const savedTheme = localStorage.getItem('theme') || 'dark';
                        html.setAttribute('data-theme', savedTheme);
                        themeSwitcher.querySelector('i').textContent =
                            savedTheme === 'dark' ? 'dark_mode' : 'light_mode';
                    });
                </script>
                <style>
                /* Base styles */
                .nav-second-level {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                    background: var(--card-bg);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 4px;
                    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.2);
                }

                /* Desktop styles (hover) */
                @media (min-width: 769px) {
                    .nav-second-level {
                        position: absolute;
                        left: 100%;
                        top: 0;
                        min-width: 200px;
                        display: none;
                    }

                    #side-menu li:hover > .nav-second-level {
                        display: block;
                        animation: fadeInUp 0.3s ease forwards;
                    }
                }

                /* Mobile styles */
                @media (max-width: 768px) {
                    .nav-second-level {
                        position: static;
                        display: none;
                        background: rgba(0, 0, 0, 0.1);
                    }

                    .nav-second-level.show {
                        display: block;
                    }

                    #side-menu li a {
                        padding: 15px 20px; /* Larger touch target */
                    }

                    .nav-second-level li a {
                        padding-left: 40px; /* Indent submenu items */
                    }
                }

                /* Animation keyframes */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(5px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                </style>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const isMobile = () => window.innerWidth <= 768;
                    const sidebar = document.querySelector('.sidebar');
                    
                    function adjustForMobile() {
                        if (isMobile()) {
                            // Force show menu text in mobile
                            const menuTexts = document.querySelectorAll('.menu-text');
                            menuTexts.forEach(text => {
                                text.style.display = 'block';
                                text.style.opacity = '1';
                            });

                            // Ensure sidebar is full width
                            sidebar.style.width = '100%';
                        } else {
                            // Reset to default desktop state
                            sidebar.style.width = '';
                            const menuTexts = document.querySelectorAll('.menu-text');
                            menuTexts.forEach(text => {
                                text.style.display = '';
                                text.style.opacity = '';
                            });
                        }
                    }

                    // Initial setup
                    adjustForMobile();

                    // Handle resize events
                    let resizeTimer;
                    window.addEventListener('resize', () => {
                        clearTimeout(resizeTimer);
                        resizeTimer = setTimeout(adjustForMobile, 250);
                    });

                    // Close submenus when clicking outside
                    document.addEventListener('click', (e) => {
                        if (isMobile() && !e.target.closest('#side-menu')) {
                            const allSubmenus = document.querySelectorAll('.nav-second-level');
                            allSubmenus.forEach(menu => menu.classList.remove('show'));
                        }
                    });
                });
                </script>
