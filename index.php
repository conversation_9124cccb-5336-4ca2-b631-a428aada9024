<?php include '../lib/config.php';
if(SITE_WORKING_STATUS){
    echo '<center style="position: relative; top: 100px;"><h1>This site is under maintenance</h1></center>';die;
}
if(isset($_SESSION['userid']) && !empty($_SESSION['userid'])){redirect('./dashboard.php');}
//redirect('../');die;
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Member Login</title>
    <link rel="shortcut icon" href="images/nexabot-logo.png" type="image/x-icon">
    <script src="https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js"></script>
    <script>
    WebFont.load({
        google: {
            families: ['Alegreya+Sans:100,100i,300,300i,400,400i,500,500i,700,700i,800,800i,900,900i', 'Raleway:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i', 'Open Sans']
        }
    });
    </script>
    <!-- Bootstrap -->
    <link href="../assets/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <!-- Bootstrap rtl -->
    <!--<link href="../assets/bootstrap-rtl/bootstrap-rtl.min.css" rel="stylesheet" type="text/css"/>-->
    <!-- Pe-icon-7-stroke -->
    <link href="../assets/pe-icon-7-stroke/css/pe-icon-7-stroke.css" rel="stylesheet" type="text/css" />
    <link href="../assets/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
    <!-- Theme style -->
    <link href="../assets/dist/css/component_ui.css" rel="stylesheet" type="text/css" />
    <link href="../assets/dist/css/skins/component_ui_black.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous">
    <!-- Theme style rtl -->
    <!--<link href="../assets/dist/css/component_ui_rtl.css" rel="stylesheet" type="text/css"/>-->
    <!-- Custom css -->
    <link href="../assets/dist/css/custom.css" rel="stylesheet" type="text/css" />
    <style>
    /* Base Styles - Modern Light Theme */
    body, #page-wrapper {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        color: #334155;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        min-height: 100vh;
    }

    /* Form Controls */
    .form-control {
        background: #ffffff;
        color: #334155;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 14px 18px;
        height: auto;
        transition: all 0.3s ease;
        font-size: 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .form-control:focus {
        background: #ffffff;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1), 0 4px 12px rgba(0, 0, 0, 0.15);
        color: #1e293b;
        outline: none;
    }

    .form-control::placeholder {
        color: #94a3b8;
        opacity: 1;
    }

    .form-control:-ms-input-placeholder {
        color: #94a3b8;
    }

    .form-control::-ms-input-placeholder {
        color: #94a3b8;
    }

    /* Modern Light Form Container */
    .form-signin {
        background: #ffffff !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 20px !important;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 4px 12px rgba(0, 0, 0, 0.05) !important;
        position: relative;
        backdrop-filter: blur(10px);
    }

    .form-signin::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #4f46e5, #7c3aed, #06b6d4);
        border-radius: 20px 20px 0 0;
    }

    .form-signin::after {
        content: '';
        position: absolute;
        bottom: -100px;
        right: -100px;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(79, 70, 229, 0.05) 0%, transparent 70%);
        border-radius: 50%;
        z-index: 0;
    }

    /* Button Styling */
    .btn {
        padding: 14px 28px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 15px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        z-index: 1;
        text-transform: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
        border: none !important;
        color: #ffffff !important;
        box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
    }

    .btn-primary:hover, .btn-primary:focus {
        background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%) !important;
        transform: translateY(-2px);
        box-shadow: 0 12px 25px rgba(79, 70, 229, 0.4);
        color: #ffffff !important;
    }

    .btn-primary::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transform: rotate(30deg);
        transition: all 0.5s ease;
        opacity: 0;
    }

    .btn-primary:hover::before {
        animation: shimmerEffect 1.5s infinite;
    }

    @keyframes shimmerEffect {
        0% {
            transform: translateX(-100%) rotate(30deg);
            opacity: 0.5;
        }
        100% {
            transform: translateX(100%) rotate(30deg);
            opacity: 0;
        }
    }

    /* Modern Light Theme Elements */
    .trading-theme-element {
        position: absolute;
        width: 120px;
        height: 120px;
        background: radial-gradient(circle, rgba(79, 70, 229, 0.08) 0%, transparent 70%);
        border-radius: 50%;
        z-index: 0;
        animation: floatAnimation 6s infinite alternate ease-in-out;
    }

    .element-1 {
        top: -60px;
        left: -60px;
        animation-delay: 0s;
    }

    .element-2 {
        bottom: -60px;
        right: -60px;
        animation-delay: 2s;
    }

    .element-3 {
        top: 50%;
        right: -60px;
        animation-delay: 4s;
    }

    @keyframes floatAnimation {
        0% { transform: translate(0, 0) scale(1); opacity: 0.3; }
        100% { transform: translate(8px, 8px) scale(1.05); opacity: 0.6; }
    }

    /* Text Styling */
    h5 {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 24px;
        background: linear-gradient(135deg, #4f46e5, #7c3aed);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;
    }

    @keyframes titleGlow {
        0% { text-shadow: 0 0 5px rgba(79, 70, 229, 0.3); }
        100% { text-shadow: 0 0 15px rgba(79, 70, 229, 0.6); }
    }

    /* Form Elements */
    .form-check-label {
        color: #64748b !important;
        font-weight: 500;
    }

    .form-check-input {
        border: 2px solid #cbd5e1;
        border-radius: 6px;
    }

    .form-check-input:checked {
        background-color: #4f46e5 !important;
        border-color: #4f46e5 !important;
    }

    .form-check-input:focus {
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    /* Links */
    a {
        color: #4f46e5 !important;
        text-decoration: none;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    a:hover {
        color: #4338ca !important;
        text-decoration: underline;
    }

    /* Background Overlay */
    .bg-overlay {
        background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(226, 232, 240, 0.95) 100%) !important;
    }

    /* Input with Icon */
    .input-with-icon {
        position: relative;
        margin-bottom: 24px;
    }

    .input-with-icon i {
        position: absolute;
        left: 18px;
        top: 72%;
        transform: translateY(-50%);
        color: #94a3b8;
        font-size: 16px;
        transition: all 0.3s ease;
        z-index: 2;
    }

    .input-with-icon input {
        padding-left: 50px;
    }

    .input-with-icon input:focus + i {
        color: #4f46e5;
    }

    /* Label Styling */
    label {
        color: #475569;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
    }
    </style>
</head>

<body>
    <!-- Content Wrapper -->
    <?php /* <div class="login-wrapper">
            <div class="container-center">
                <div class="panel panel-bd">
                    <div class="panel-heading">
                        <div class="view-header">
                            <!--<div class="header-icon">
                                <i class="pe-7s-unlock"></i>
                            </div>-->
                            <div class="header-title">
                                <img src="images/lizacoin.png" style="width: 80%;">
                            </div>
                            <br>
                            <div class="header-title">
                                <h3>Login</h3>
                                <small><strong>Please enter your credentials to login.</strong></small>
                            </div>
                        </div>
                    </div>
                   <div class="panel-body">
                        <form action="login_model.php" id="loginForm" novalidate  method="post">
                            <?php echo getMessage();?>
    <!--Social Buttons-->

    <div class="social">
        <strong>Sign in using social network:</strong><br>
        <div class="twitter_bg"><i class="fa fa-twitter"></i><a href="#" class="btn_1">Login Twitter</a></div>
        <div class="fb_bg"><i class="fa fa-facebook"></i><a href="#" class="btn_2">Login Facebook</a></div>
    </div>

    <div class="form-group">
        <label class="control-label">User Id</label>
        <div class="input-group">
            <span class="input-group-addon"><i class="glyphicon glyphicon-user"></i></span>
            <input type="text" class="form-control" id="login_id" name="login_id" placeholder="User Id" maxlength="20">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label">Password</label>
        <div class="input-group">
            <span class="input-group-addon"><i class="fa fa-key"></i></span>
            <input type="password" class="form-control" id="password" name="password" placeholder="******" maxlength="20">
        </div>
    </div>
    <div>
        <button type="submit" class="btn btn-primary pull-right">Login</button>
        <div class="checkbox checkbox-success">
            <input id="checkbox3" type="checkbox">
            <label for="checkbox3">Keep me signed in</label>
        </div>
    </div>
    </form>
    </div>
    </div>

    <div id="bottom_text">
        Don't have an account? <a href="register.php">Sign Up</a><br>
        Remind <a href="forgot.php">Password</a><br>
        Go to <a href="../../">Home</a>
    </div>

    </div>
    </div>
    */ ?>


    <?php /* <div class="wrapper">
        <form action="login_model.php" id="loginForm" novalidate  method="post">
            <?php echo getMessage();?>
            <img src="images/BLC.png" style="width: 43%; margin-left: 27%;">
            <!--<h1>Login</h1>-->
            <div class="input-box">
                <input type="text" class="form-control" id="login_id" name="login_id" placeholder="User Id" maxlength="20">
                <i class="fas fa-user"></i>
            </div>
            <div class="input-box">
                <input type="password" class="form-control" id="password" name="password" placeholder="******" maxlength="20">
                <i class="fas fa-lock"></i>
            </div>
            <div class="remember-forgot">
                <label><input type="checkbox">Remember me
                </label>
                <a href="forgot.php">Forgot password?</a>
            </div>
            <button type="submit" class="btn">Login</button>
            <div class="register-link">
                <p>Dont't have an account? <a href="register.php">
                        Register
                    </a></p>
            </div>
        </form>
    </div>
 */ ?>


 <style>
     body {
        display: unset !important;
        background-image: unset !important;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
        min-height: 100vh;
     }

     /* Additional Light Theme Enhancements */
     .avatar {
        border-radius: 16px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
     }

     .avatar:hover {
        transform: scale(1.05);
        box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
     }

     /* Section Background */
     .bg-home {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
     }

     /* Form Container Max Width */
     .form-signin {
        max-width: 420px;
        margin: 0 auto;
     }

     /* Button Icon Spacing */
     .btn i {
        margin-right: 8px;
     }

     /* Additional Form Styling */
     .d-flex.justify-content-between {
        align-items: center;
     }

     .text-center.mt-4 {
        padding-top: 20px;
        border-top: 1px solid #e2e8f0;
     }

     .d-flex.justify-content-center.align-items-center span {
        color: #64748b;
        font-weight: 500;
     }

     /* Responsive Adjustments */
     @media (max-width: 768px) {
        .form-signin {
            margin: 20px;
            padding: 30px 20px !important;
        }

        h5 {
            font-size: 24px;
        }

        .input-with-icon input {
            padding: 16px 18px 16px 50px;
        }
     }
 </style>







  <link href="dist/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <link href="dist/css/materialdesignicons.min.css" rel="stylesheet" type="text/css" />
        <link href="https://unicons.iconscout.com/release/v4.0.0/css/line.css" rel="stylesheet">
        <!-- Main css -->
        <link href="dist/css/style.css" rel="stylesheet" type="text/css" id="theme-opt" />
        <link href="dist/css/colors/default.css" rel="stylesheet" id="color-opt">
         <!-- javascript -->
        <script src="dist/js/bootstrap.bundle.min.js"></script>
        <script src="dist/js/feather.min.js"></script>

        <!-- Main Js -->
        <script src="dist/js/plugins.init.js"></script>
        <script src="dist/js/app.js"></script>

  <section class="bg-home d-flex align-items-center position-relative" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); min-height: 100vh;">
            <div class="bg-overlay opacity-8"></div>
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="form-signin p-4 rounded shadow-md">
                            <!-- Trading Theme Elements -->
                            <!--<div class="trading-theme-element element-1"></div>-->
                            <!--<div class="trading-theme-element element-2"></div>-->
                            <!--<div class="trading-theme-element element-3"></div>-->

                            <form action="login_model.php" id="loginForm" novalidate method="post">
                                <?php echo getMessage();?>
                                <a href="index.php"><img src="images/nexabot-logo.png" class="avatar avatar-md-md mb-4 d-block mx-auto" alt=""></a>
                                <h5 class="mb-4 text-center">Welcome Back</h5>

                                <div class="input-with-icon mb-3">
                                    <label for="login_id">Username</label>
                                    <input type="text" class="form-control" id="login_id" name="login_id" placeholder="Enter your username" maxlength="20">
                                    <i class="fa fa-user"></i>
                                </div>

                                <div class="input-with-icon mb-3">
                                    <label for="password">Password</label>
                                    <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" maxlength="20">
                                    <i class="fa fa-lock"></i>
                                </div>

                                <div class="d-flex justify-content-between mb-4">
                                    <div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                            <label class="form-check-label" for="flexCheckDefault">Remember me</label>
                                        </div>
                                    </div>
                                    <div>
                                        <a href="forgot.php" class="fw-medium">Forgot password?</a>
                                    </div>
                                </div>

                                <button class="btn btn-primary w-100" type="submit">
                                    <i class="fa fa-sign-in-alt me-2"></i> Login to Dashboard
                                </button>

                                <a href="register.php" class="fw-medium"><div class="col-12 text-center mt-4" >
                                    <div class="d-flex justify-content-center align-items-center">
                                        <span class="me-2">Don't have an account?</span>
                                        
                                            <!--<input type="button" value="Register Now"/>-->
                                            <span>Register Now</span>
                                        
                                    </div>
                                </div></a>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>




    <!-- /.content-wrapper -->
    <!-- jQuery -->
    <script src="../assets/plugins/jQuery/jquery-1.12.4.min.js"></script>
    <!-- bootstrap js -->
    <script src="../assets/bootstrap/js/bootstrap.min.js"></script>
</body>

</html>